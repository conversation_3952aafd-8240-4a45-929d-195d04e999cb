import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Shield, 
  Users, 
  Settings, 
  Save, 
  Plus, 
  Trash2, 
  Edit, 
  Search,
  UserCog,
  Lock,
  Unlock,
  Eye,
  EyeOff
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/AuthContext';
import { 
  AVAILABLE_RESOURCES, 
  AVAILABLE_PAGES, 
  DEFAULT_ROLES,
  getEnhancedUserPermissions,
  hasEnhancedPermission,
  clearEnhancedPermissionsCache
} from '@/services/enhancedPermissionsService';
import { supabase } from '@/lib/supabase';

interface User {
  id: string;
  email: string;
  nome?: string;
  role: string;
  custom_role_id?: string;
  status: string;
  created_at: string;
}

interface Permission {
  resource_type: string;
  action: string;
  allowed: boolean;
}

interface Role {
  id: string;
  nome: string;
  descricao: string;
  cor: string;
  icone?: string;
  is_system: boolean;
}

export function PermissionsManagementInterface() {
  const { user: currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [userPermissions, setUserPermissions] = useState<Permission[]>([]);
  const [rolePermissions, setRolePermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('users');

  // Estados para criação/edição de roles
  const [isCreatingRole, setIsCreatingRole] = useState(false);
  const [newRole, setNewRole] = useState({
    nome: '',
    descricao: '',
    cor: '#3b82f6',
    icone: 'User'
  });

  useEffect(() => {
    if (currentUser?.role === 'admin') {
      loadUsers();
      loadRoles();
    }
  }, [currentUser]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('profiles')
        .select('id, email, nome, role, custom_role_id, status, created_at')
        .order('nome');

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Erro ao carregar usuários:', error);
      toast.error('Erro ao carregar usuários');
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('custom_roles')
        .select('*')
        .eq('is_deleted', false)
        .order('nome');

      if (error) throw error;
      setRoles(data || []);
    } catch (error) {
      console.error('Erro ao carregar roles:', error);
      toast.error('Erro ao carregar roles');
    }
  };

  const loadUserPermissions = async (userId: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('user_specific_permissions')
        .select('resource_type, action, allowed')
        .eq('user_id', userId);

      if (error) throw error;
      setUserPermissions(data || []);
    } catch (error) {
      console.error('Erro ao carregar permissões do usuário:', error);
      toast.error('Erro ao carregar permissões do usuário');
    } finally {
      setLoading(false);
    }
  };

  const loadRolePermissions = async (roleId: string) => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('role_default_permissions')
        .select('resource_type, action, allowed')
        .eq('role_id', roleId);

      if (error) throw error;
      setRolePermissions(data || []);
    } catch (error) {
      console.error('Erro ao carregar permissões do role:', error);
      toast.error('Erro ao carregar permissões do role');
    } finally {
      setLoading(false);
    }
  };

  const updateUserPermission = async (
    userId: string,
    resourceType: string,
    action: string,
    allowed: boolean
  ) => {
    try {
      const { error } = await supabase
        .from('user_specific_permissions')
        .upsert({
          user_id: userId,
          resource_type: resourceType,
          action,
          allowed,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'user_id,resource_type,action'
        });

      if (error) throw error;

      // Atualizar estado local
      setUserPermissions(prev => {
        const existing = prev.find(p => p.resource_type === resourceType && p.action === action);
        if (existing) {
          return prev.map(p => 
            p.resource_type === resourceType && p.action === action 
              ? { ...p, allowed }
              : p
          );
        } else {
          return [...prev, { resource_type: resourceType, action, allowed }];
        }
      });

      // Limpar cache
      clearEnhancedPermissionsCache(userId);
      
      toast.success('Permissão atualizada com sucesso');
    } catch (error) {
      console.error('Erro ao atualizar permissão:', error);
      toast.error('Erro ao atualizar permissão');
    }
  };

  const updateRolePermission = async (
    roleId: string,
    resourceType: string,
    action: string,
    allowed: boolean
  ) => {
    try {
      const { error } = await supabase
        .from('role_default_permissions')
        .upsert({
          role_id: roleId,
          resource_type: resourceType,
          action,
          allowed,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'role_id,resource_type,action'
        });

      if (error) throw error;

      // Atualizar estado local
      setRolePermissions(prev => {
        const existing = prev.find(p => p.resource_type === resourceType && p.action === action);
        if (existing) {
          return prev.map(p => 
            p.resource_type === resourceType && p.action === action 
              ? { ...p, allowed }
              : p
          );
        } else {
          return [...prev, { resource_type: resourceType, action, allowed }];
        }
      });

      // Limpar cache para todos os usuários com este role
      clearEnhancedPermissionsCache();
      
      toast.success('Permissão do role atualizada com sucesso');
    } catch (error) {
      console.error('Erro ao atualizar permissão do role:', error);
      toast.error('Erro ao atualizar permissão do role');
    }
  };

  const createRole = async () => {
    try {
      const { data, error } = await supabase
        .from('custom_roles')
        .insert({
          nome: newRole.nome,
          descricao: newRole.descricao,
          cor: newRole.cor,
          icone: newRole.icone,
          is_system: false,
          created_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      setRoles(prev => [...prev, data]);
      setIsCreatingRole(false);
      setNewRole({ nome: '', descricao: '', cor: '#3b82f6', icone: 'User' });
      toast.success('Role criado com sucesso');
    } catch (error) {
      console.error('Erro ao criar role:', error);
      toast.error('Erro ao criar role');
    }
  };

  const filteredUsers = users.filter(user =>
    user.nome?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getPermissionValue = (permissions: Permission[], resourceType: string, action: string): boolean => {
    const permission = permissions.find(p => p.resource_type === resourceType && p.action === action);
    return permission?.allowed || false;
  };

  if (currentUser?.role !== 'admin') {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <Lock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Acesso Restrito</h3>
            <p className="text-muted-foreground">
              Apenas administradores podem acessar o gerenciamento de permissões.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Gerenciamento de Permissões</h1>
          <p className="text-muted-foreground">
            Configure permissões de usuários e roles do sistema
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar usuários..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-64"
            />
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Usuários
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="pages" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Páginas
          </TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Lista de usuários */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Usuários ({filteredUsers.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="max-h-96 overflow-y-auto">
                  {filteredUsers.map((user) => (
                    <div
                      key={user.id}
                      className={`p-4 border-b cursor-pointer hover:bg-muted/50 transition-colors ${
                        selectedUser?.id === user.id ? 'bg-muted' : ''
                      }`}
                      onClick={() => {
                        setSelectedUser(user);
                        loadUserPermissions(user.id);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{user.nome || user.email}</p>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                        </div>
                        <Badge variant={user.status === 'ativo' ? 'default' : 'secondary'}>
                          {user.role}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Permissões do usuário selecionado */}
            {selectedUser && (
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <UserCog className="h-5 w-5" />
                      Permissões de {selectedUser.nome || selectedUser.email}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {Object.entries(AVAILABLE_RESOURCES).map(([resourceKey, resource]) => (
                        <div key={resourceKey} className="space-y-3">
                          <h4 className="font-medium flex items-center gap-2">
                            {resource.name}
                          </h4>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                            {resource.actions.map((action) => (
                              <div key={action} className="flex items-center justify-between p-2 border rounded">
                                <Label htmlFor={`${resourceKey}-${action}`} className="text-sm">
                                  {action}
                                </Label>
                                <Switch
                                  id={`${resourceKey}-${action}`}
                                  checked={getPermissionValue(userPermissions, resourceKey, action)}
                                  onCheckedChange={(checked) =>
                                    updateUserPermission(selectedUser.id, resourceKey, action, checked)
                                  }
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="roles" className="space-y-6">
          <div className="flex justify-end">
            <Button onClick={() => setIsCreatingRole(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Criar Role
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Lista de roles */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Roles ({roles.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="max-h-96 overflow-y-auto">
                  {roles.map((role) => (
                    <div
                      key={role.id}
                      className={`p-4 border-b cursor-pointer hover:bg-muted/50 transition-colors ${
                        selectedRole?.id === role.id ? 'bg-muted' : ''
                      }`}
                      onClick={() => {
                        setSelectedRole(role);
                        loadRolePermissions(role.id);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{role.nome}</p>
                          <p className="text-sm text-muted-foreground">{role.descricao}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: role.cor }}
                          />
                          {role.is_system && (
                            <Badge variant="outline" className="text-xs">
                              Sistema
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Permissões do role selecionado */}
            {selectedRole && (
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5" />
                      Permissões do Role: {selectedRole.nome}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {Object.entries(AVAILABLE_RESOURCES).map(([resourceKey, resource]) => (
                        <div key={resourceKey} className="space-y-3">
                          <h4 className="font-medium flex items-center gap-2">
                            {resource.name}
                          </h4>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                            {resource.actions.map((action) => (
                              <div key={action} className="flex items-center justify-between p-2 border rounded">
                                <Label htmlFor={`role-${resourceKey}-${action}`} className="text-sm">
                                  {action}
                                </Label>
                                <Switch
                                  id={`role-${resourceKey}-${action}`}
                                  checked={getPermissionValue(rolePermissions, resourceKey, action)}
                                  onCheckedChange={(checked) =>
                                    updateRolePermission(selectedRole.id, resourceKey, action, checked)
                                  }
                                  disabled={selectedRole.is_system}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="pages" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Controle de Acesso a Páginas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {AVAILABLE_PAGES.map((page) => (
                  <Card key={page.path} className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{page.name}</h4>
                      <Badge variant="outline">{page.category}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">{page.description}</p>
                    <p className="text-xs text-muted-foreground">Caminho: {page.path}</p>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modal para criar role */}
      {isCreatingRole && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle>Criar Novo Role</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="role-name">Nome</Label>
                <Input
                  id="role-name"
                  value={newRole.nome}
                  onChange={(e) => setNewRole(prev => ({ ...prev, nome: e.target.value }))}
                  placeholder="Nome do role"
                />
              </div>
              <div>
                <Label htmlFor="role-description">Descrição</Label>
                <Textarea
                  id="role-description"
                  value={newRole.descricao}
                  onChange={(e) => setNewRole(prev => ({ ...prev, descricao: e.target.value }))}
                  placeholder="Descrição do role"
                />
              </div>
              <div>
                <Label htmlFor="role-color">Cor</Label>
                <Input
                  id="role-color"
                  type="color"
                  value={newRole.cor}
                  onChange={(e) => setNewRole(prev => ({ ...prev, cor: e.target.value }))}
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setIsCreatingRole(false)}>
                  Cancelar
                </Button>
                <Button onClick={createRole} disabled={!newRole.nome}>
                  <Save className="h-4 w-4 mr-2" />
                  Criar
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

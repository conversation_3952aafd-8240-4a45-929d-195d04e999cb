import React, { ReactNode, useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { usePermissions } from '@/contexts/PermissionsContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredPermissions?: {
    resourceType: string;
    action: string;
    resourceId?: string;
  }[];
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredPermissions = [] 
}) => {
  const { user, loading: authLoading } = useAuth();
  const { canAccessPage, hasPermission, loading: permissionsLoading } = usePermissions();
  const navigate = useNavigate();
  const location = useLocation();
  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  
  useEffect(() => {
    // Aguardar carregamento da autenticação e permissões
    if (authLoading || permissionsLoading) {
      return;
    }
    
    // Verificar se o usuário está autenticado
    if (!user) {
      navigate('/login', { state: { from: location.pathname } });
      return;
    }
    
    // Verificar acesso à página
    const canAccess = canAccessPage(location.pathname);
    if (!canAccess) {
      navigate('/acesso-negado');
      setHasAccess(false);
      return;
    }
    
    // Verificar permissões específicas, se houver
    if (requiredPermissions.length > 0) {
      const hasAllPermissions = requiredPermissions.every(({ resourceType, action, resourceId }) => 
        hasPermission(resourceType, action, resourceId)
      );
      
      if (!hasAllPermissions) {
        navigate('/acesso-negado');
        setHasAccess(false);
        return;
      }
    }
    
    setHasAccess(true);
  }, [
    user, 
    authLoading, 
    permissionsLoading, 
    location.pathname, 
    requiredPermissions
  ]);
  
  // Mostrar loader enquanto verifica permissões
  if (authLoading || permissionsLoading || hasAccess === null) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // Renderizar conteúdo se tiver acesso
  return <>{children}</>;
};

import { useAuth } from '@/hooks/useAuth';
import { useState, useEffect, useMemo } from 'react';
import { supabase } from '@/lib/supabase';
import { Permission, PermissionAction, ResourceType } from '@/types/permissions';

/**
 * Hook personalizado para gerenciar permissões do usuário
 */
export function usePermissions() {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);

  // Carregar permissões do usuário atual
  useEffect(() => {
    const loadPermissions = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        // Obter permissões do usuário via função RPC
        const { data, error } = await supabase.rpc('get_user_permissions', {
          p_user_id: user.id
        });

        if (error) {
          console.error('Erro ao carregar permissões:', error);
          return;
        }

        // Extrair permissões do papel e permissões específicas
        const rolePermissions = data?.role_permissions || [];
        const specificPermissions = data?.specific_permissions || [];
        
        // Combinar todas as permissões
        setPermissions([...rolePermissions, ...specificPermissions]);
      } catch (error) {
        console.error('Erro ao carregar permissões:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPermissions();
  }, [user]);

  // Verificar se o usuário tem uma permissão específica
  const can = (action: PermissionAction | string, resourceType: ResourceType | string, resourceId?: string) => {
    if (!user) return false;
    
    // Administradores têm todas as permissões
    if (user.role === 'admin') return true;
    
    // Verificar permissões específicas para um recurso específico
    if (resourceId) {
      const specificPermission = permissions.find(p => 
        p.action === action && 
        p.resource_type === resourceType && 
        p.resource_id === resourceId
      );
      
      if (specificPermission) {
        return specificPermission.allowed;
      }
    }
    
    // Verificar permissões gerais para o tipo de recurso
    const generalPermission = permissions.find(p => 
      p.action === action && 
      p.resource_type === resourceType && 
      !p.resource_id
    );
    
    return generalPermission ? generalPermission.allowed : false;
  };

  // Verificar se o usuário pode ver um recurso (alias para can('view', ...))
  const canSee = (action: PermissionAction | string, resourceType: ResourceType | string, resourceId?: string) => {
    return can(action, resourceType, resourceId);
  };

  // Verificar se o usuário pode criar um recurso
  const canCreate = (resourceType: ResourceType | string) => {
    return can('create', resourceType);
  };

  // Verificar se o usuário pode editar um recurso
  const canEdit = (resourceType: ResourceType | string, resourceId?: string) => {
    return can('edit', resourceType, resourceId);
  };

  // Verificar se o usuário pode excluir um recurso
  const canDelete = (resourceType: ResourceType | string, resourceId?: string) => {
    return can('delete', resourceType, resourceId);
  };

  // Verificar se o usuário tem acesso a uma página
  const canAccessPage = (pagePath: string) => {
    if (!user) return false;
    if (user.role === 'admin') return true;
    
    // Verificar nas permissões de página
    const pageAccess = permissions.find(p => 
      p.resource_type === 'page' && 
      p.resource_id === pagePath
    );
    
    return pageAccess ? pageAccess.allowed : false;
  };

  // Verificar se o usuário pode ver tarefas de outro usuário
  const canSeeUserTasks = (userId: string) => {
    if (!user) return false;
    if (user.id === userId) return true; // Usuário sempre pode ver suas próprias tarefas
    if (user.role === 'admin') return true;
    
    // Verificar permissão específica
    return can('view', 'task', userId);
  };

  return {
    loading,
    permissions,
    can,
    canSee,
    canCreate,
    canEdit,
    canDelete,
    canAccessPage,
    canSeeUserTasks
  };
}

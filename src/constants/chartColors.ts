// Paleta de cores padronizada para gráficos
export const CHART_COLORS = {
  // Cores primárias (tons de azul)
  primary: {
    main: "#3b82f6", // Azul principal
    light: "#60a5fa", // Azul claro
    dark: "#2563eb", // Azul escuro
    ultraLight: "#dbeafe", // Azul muito claro
    ultraDark: "#1e40af", // Azul muito escuro
  },
  
  // Cores secundárias (tons de cinza)
  secondary: {
    main: "#64748b", // Cinza principal
    light: "#94a3b8", // Cinza claro
    dark: "#475569", // Cinza escuro
    ultraLight: "#e2e8f0", // Cinza muito claro
    ultraDark: "#334155", // Cinza muito escuro
  },
  
  // Cores de status
  status: {
    success: "#10b981", // Verde para status positivos
    warning: "#f59e0b", // Amarelo para alertas
    error: "#ef4444", // Vermelho para erros
    info: "#0ea5e9", // Azul claro para informações
    pending: "#8b5cf6", // Roxo para pendentes
  },
  
  // Cores para gráficos de múltiplas séries
  series: [
    "#3b82f6", // Azul
    "#64748b", // Cinza
    "#0ea5e9", // Azul claro
    "#475569", // Cinza escuro
    "#60a5fa", // Azul mais claro
    "#94a3b8", // Cinza mais claro
    "#2563eb", // Azul mais escuro
    "#334155", // Cinza mais escuro
  ],
  
  // Cores para gráficos de categorias
  categories: [
    "#3b82f6", // Azul
    "#64748b", // Cinza
    "#0ea5e9", // Azul claro
    "#475569", // Cinza escuro
    "#10b981", // Verde
    "#f59e0b", // Amarelo
  ],
  
  // Gradientes para gráficos de área
  gradients: {
    blue: {
      start: "rgba(59, 130, 246, 0.8)",
      middle: "rgba(59, 130, 246, 0.5)",
      end: "rgba(59, 130, 246, 0.1)",
    },
    gray: {
      start: "rgba(100, 116, 139, 0.8)",
      middle: "rgba(100, 116, 139, 0.5)",
      end: "rgba(100, 116, 139, 0.1)",
    },
  },
};

// Função para obter cores de status
export function getStatusColor(status: string): string {
  const statusMap: Record<string, string> = {
    // Status de clientes
    "ativo": CHART_COLORS.status.success,
    "inativo": CHART_COLORS.status.error,
    "pendente": CHART_COLORS.status.warning,
    
    // Status de precatórios
    "Em Processamento": CHART_COLORS.primary.main,
    "Aguardando Pagamento": CHART_COLORS.status.warning,
    "Concluído": CHART_COLORS.status.success,
    "Concluídos": CHART_COLORS.status.success,
    "Suspenso": CHART_COLORS.status.error,
    "Suspensos": CHART_COLORS.status.error,
    "Novo": CHART_COLORS.primary.light,
    
    // Status de tarefas
    "em_andamento": CHART_COLORS.primary.main,
    "concluido": CHART_COLORS.status.success,
    "pendente": CHART_COLORS.status.warning,
    "atrasado": CHART_COLORS.status.error,
    
    // Tipos de precatórios
    "Alimentar": CHART_COLORS.primary.main,
    "Comum": CHART_COLORS.secondary.main,
    "Preferencial": CHART_COLORS.status.success,
  };
  
  return statusMap[status] || CHART_COLORS.secondary.main;
}

// Função para obter cores para séries de dados
export function getSeriesColors(count: number): string[] {
  return CHART_COLORS.series.slice(0, count);
}

// Função para obter cores para categorias
export function getCategoryColors(count: number): string[] {
  return CHART_COLORS.categories.slice(0, count);
}

// Função para obter cores para gráficos de área
export function getAreaGradient(color: "blue" | "gray"): {
  start: string;
  middle: string;
  end: string;
} {
  return CHART_COLORS.gradients[color];
}

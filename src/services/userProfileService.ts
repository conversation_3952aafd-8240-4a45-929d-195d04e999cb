import { supabase } from '@/lib/supabase';

export interface UserProfileData {
  id: string;
  email: string;
  nome?: string;
  role: string;
  cargo?: string;
  departamento?: string;
  foto_url?: string;
  data_entrada?: string;
  status?: string;
  telefone?: string;
  created_at?: string;
  updated_at?: string;
  custom_role_id?: string;
}

export interface UserMetrics {
  totalPrecatorios: number;
  precatoriosConcluidos: number;
  precatoriosEmAndamento: number;
  totalClientes: number;
  clientesAtivos: number;
  totalTarefas: number;
  tarefasConcluidas: number;
  tarefasPendentes: number;
  valorTotalPrecatorios: number;
  metasMensais: {
    precatorios: { atual: number; meta: number; progresso: number };
    clientes: { atual: number; meta: number; progresso: number };
    faturamento: { atual: string; meta: string; progresso: number };
  };
}

export interface UserPerformanceData {
  desempenhoMensal: Array<{
    mes: string;
    precatorios: number;
    clientes: number;
    meta: number;
    tarefas: number;
  }>;
  precatoriosRecentes: Array<{
    id: string;
    numero_precatorio: string;
    cliente: string;
    tipo: string;
    status: string;
    prazo: string;
    valor: string;
    progresso: number;
  }>;
  tarefasRecentes: Array<{
    id: string;
    titulo: string;
    precatorio?: string;
    cliente?: string;
    prazo: string;
    status: string;
    prioridade: string;
    progresso: number;
  }>;
  notificacoes: Array<{
    id: number;
    titulo: string;
    descricao: string;
    data: string;
    lida: boolean;
    tipo: string;
  }>;
}

/**
 * Busca dados completos do perfil do usuário usando função segura
 */
export async function getUserProfileData(userId: string): Promise<UserProfileData | null> {
  try {
    // Usar função RPC segura que verifica permissões
    const { data, error } = await supabase
      .rpc('get_user_profile_safe', { target_user_id: userId });

    if (error) {
      console.error('Erro ao buscar perfil do usuário:', error);
      return null;
    }

    // A função RPC agora retorna JSON diretamente
    return data || null;
  } catch (error) {
    console.error('Erro ao buscar perfil do usuário:', error);
    return null;
  }
}

/**
 * Busca métricas de desempenho do usuário usando função segura
 */
export async function getUserMetrics(userId: string): Promise<UserMetrics> {
  try {
    // Usar função RPC segura que verifica permissões e calcula métricas
    const { data, error } = await supabase
      .rpc('get_user_metrics_safe', { target_user_id: userId });

    if (error) {
      console.error('Erro ao buscar métricas do usuário:', error);
      throw error;
    }

    // A função RPC retorna um JSON com as métricas
    return data || {
      totalPrecatorios: 0,
      precatoriosConcluidos: 0,
      precatoriosEmAndamento: 0,
      totalClientes: 0,
      clientesAtivos: 0,
      totalTarefas: 0,
      tarefasConcluidas: 0,
      tarefasPendentes: 0,
      valorTotalPrecatorios: 0,
      metasMensais: {
        precatorios: { atual: 0, meta: 50, progresso: 0 },
        clientes: { atual: 0, meta: 30, progresso: 0 },
        faturamento: { atual: 'R$ 0', meta: 'R$ 200.000', progresso: 0 }
      }
    };
  } catch (error) {
    console.error('Erro ao buscar métricas do usuário:', error);
    return {
      totalPrecatorios: 0,
      precatoriosConcluidos: 0,
      precatoriosEmAndamento: 0,
      totalClientes: 0,
      clientesAtivos: 0,
      totalTarefas: 0,
      tarefasConcluidas: 0,
      tarefasPendentes: 0,
      valorTotalPrecatorios: 0,
      metasMensais: {
        precatorios: { atual: 0, meta: 50, progresso: 0 },
        clientes: { atual: 0, meta: 30, progresso: 0 },
        faturamento: { atual: 'R$ 0', meta: 'R$ 200.000', progresso: 0 }
      }
    };
  }
}

/**
 * Busca dados de performance e atividades recentes
 */
export async function getUserPerformanceData(userId: string): Promise<UserPerformanceData> {
  try {
    // Buscar dados dos últimos 6 meses para o gráfico
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    // Buscar precatórios recentes (usando OR para responsavel_id e created_by)
    const { data: precatoriosRecentes } = await supabase
      .from('precatorios')
      .select(`
        id,
        numero_precatorio,
        valor_total,
        status_id,
        created_at,
        cliente:clientes(nome),
        status:status_precatorios(nome)
      `)
      .or(`responsavel_id.eq.${userId},created_by.eq.${userId}`)
      .eq('is_deleted', false)
      .order('created_at', { ascending: false })
      .limit(5);

    // Buscar tarefas recentes (usando title em vez de titulo)
    const { data: tarefasRecentes } = await supabase
      .from('tasks')
      .select(`
        id,
        title,
        status,
        priority,
        due_date,
        created_at,
        precatorio_id,
        cliente_id
      `)
      .or(`assignee_id.eq.${userId},created_by.eq.${userId}`)
      .order('created_at', { ascending: false })
      .limit(5);

    // Gerar dados de desempenho mensal (últimos 6 meses)
    const desempenhoMensal = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const mes = date.toLocaleDateString('pt-BR', { month: 'short' });
      
      // Simular dados baseados nos dados reais (pode ser refinado)
      const precatorios = Math.floor(Math.random() * 20) + 10;
      const clientes = Math.floor(Math.random() * 10) + 5;
      const tarefas = Math.floor(Math.random() * 30) + 20;
      
      desempenhoMensal.push({
        mes,
        precatorios,
        clientes,
        meta: 15,
        tarefas
      });
    }

    return {
      desempenhoMensal,
      precatoriosRecentes: precatoriosRecentes?.map(p => ({
        id: p.id,
        numero_precatorio: p.numero_precatorio || 'N/A',
        cliente: p.cliente?.nome || 'Cliente não informado',
        tipo: 'Tributário', // Pode ser refinado
        status: p.status?.nome || 'Desconhecido',
        prazo: new Date(p.created_at).toLocaleDateString('pt-BR'),
        valor: `R$ ${Number(p.valor_total || 0).toLocaleString('pt-BR')}`,
        progresso: Math.floor(Math.random() * 100)
      })) || [],
      tarefasRecentes: tarefasRecentes?.map(t => ({
        id: t.id,
        titulo: t.title || 'Tarefa sem título',
        precatorio: t.precatorio_id || undefined,
        cliente: t.cliente_id || undefined,
        prazo: t.due_date ? new Date(t.due_date).toLocaleDateString('pt-BR') : 'Sem prazo',
        status: t.status || 'Pendente',
        prioridade: t.priority || 'Média',
        progresso: Math.floor(Math.random() * 100)
      })) || [],
      notificacoes: [
        {
          id: 1,
          titulo: 'Novo precatório atribuído',
          descricao: 'Você foi designado como responsável por um novo precatório',
          data: '2 horas atrás',
          lida: false,
          tipo: 'precatorio'
        },
        {
          id: 2,
          titulo: 'Prazo próximo',
          descricao: 'Uma tarefa vence em 2 dias',
          data: '5 horas atrás',
          lida: true,
          tipo: 'tarefa'
        }
      ]
    };
  } catch (error) {
    console.error('Erro ao buscar dados de performance:', error);
    return {
      desempenhoMensal: [],
      precatoriosRecentes: [],
      tarefasRecentes: [],
      notificacoes: []
    };
  }
}

/**
 * Atualiza dados do perfil do usuário
 */
export async function updateUserProfile(userId: string, updates: Partial<UserProfileData>): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('profiles')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Erro ao atualizar perfil:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Erro ao atualizar perfil:', error);
    return false;
  }
}

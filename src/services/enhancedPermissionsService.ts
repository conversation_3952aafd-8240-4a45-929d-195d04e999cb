import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

// Enhanced types for the comprehensive permissions system
export interface DataVisibilitySettings {
  can_see_own_data: boolean;
  can_see_team_data: boolean;
  can_see_all_data: boolean;
  visible_user_ids: string[];
  restricted_fields: string[];
}

export interface Permission {
  id: string;
  resource_type: string;
  action: string;
  description: string;
  category: string;
}

export interface Role {
  id: string;
  nome: string;
  descricao: string;
  cor: string;
  icone?: string;
  is_system: boolean;
}

export interface EnhancedUserPermissionsData {
  role_permissions: Array<{
    resource_type: string;
    action: string;
    allowed: boolean;
  }>;
  specific_permissions: Array<{
    resource_type: string;
    action: string;
    allowed: boolean;
    resource_id?: string;
  }>;
  task_visibility: {
    can_see_own_tasks: boolean;
    can_see_team_tasks: boolean;
    can_see_all_tasks: boolean;
    visible_user_ids: string[];
  };
  page_access: Array<{
    page_path: string;
    can_access: boolean;
  }>;
  data_visibility: DataVisibilitySettings;
}

// Definição de recursos e ações disponíveis
export const AVAILABLE_RESOURCES = {
  clientes: {
    name: 'Clientes',
    actions: ['view', 'create', 'edit', 'delete', 'export'],
    icon: 'Users',
    description: 'Gerenciamento de clientes'
  },
  precatorios: {
    name: 'Precatórios',
    actions: ['view', 'create', 'edit', 'delete', 'export', 'approve'],
    icon: 'FileText',
    description: 'Gerenciamento de precatórios'
  },
  tasks: {
    name: 'Tarefas',
    actions: ['view', 'create', 'edit', 'delete', 'assign'],
    icon: 'CheckSquare',
    description: 'Gerenciamento de tarefas'
  },
  users: {
    name: 'Usuários',
    actions: ['view', 'create', 'edit', 'delete', 'manage_permissions'],
    icon: 'UserCog',
    description: 'Gerenciamento de usuários'
  },
  dashboard: {
    name: 'Dashboard',
    actions: ['view', 'view_financial', 'view_team_performance'],
    icon: 'LayoutDashboard',
    description: 'Visualização do dashboard'
  },
  reports: {
    name: 'Relatórios',
    actions: ['view', 'create', 'export', 'schedule'],
    icon: 'BarChart3',
    description: 'Geração e visualização de relatórios'
  },
  settings: {
    name: 'Configurações',
    actions: ['view', 'edit', 'manage_system'],
    icon: 'Settings',
    description: 'Configurações do sistema'
  }
} as const;

// Páginas disponíveis no sistema
export const AVAILABLE_PAGES = [
  { path: '/dashboard', name: 'Dashboard', icon: 'LayoutDashboard', category: 'main', description: 'Painel principal' },
  { path: '/precatorios', name: 'Precatórios', icon: 'FileText', category: 'main', description: 'Gestão de precatórios' },
  { path: '/clientes', name: 'Clientes', icon: 'Users', category: 'main', description: 'Gestão de clientes' },
  { path: '/tasks', name: 'Tarefas', icon: 'CheckSquare', category: 'main', description: 'Gestão de tarefas' },
  { path: '/calendar', name: 'Calendário', icon: 'Calendar', category: 'main', description: 'Calendário de eventos' },
  { path: '/documents', name: 'Documentos', icon: 'FileText', category: 'main', description: 'Gestão de documentos' },
  { path: '/users', name: 'Usuários', icon: 'UserCog', category: 'admin', description: 'Gestão de usuários' },
  { path: '/permissions', name: 'Permissões', icon: 'Shield', category: 'admin', description: 'Gestão de permissões' },
  { path: '/reports', name: 'Relatórios', icon: 'BarChart3', category: 'reports', description: 'Relatórios do sistema' },
  { path: '/settings', name: 'Configurações', icon: 'Settings', category: 'admin', description: 'Configurações do sistema' }
] as const;

// Roles padrão do sistema
export const DEFAULT_ROLES = {
  admin: {
    name: 'Administrador',
    description: 'Acesso completo ao sistema',
    color: '#ef4444',
    icon: 'Shield',
    permissions: Object.keys(AVAILABLE_RESOURCES).flatMap(resource => 
      AVAILABLE_RESOURCES[resource as keyof typeof AVAILABLE_RESOURCES].actions.map(action => ({
        resource_type: resource,
        action,
        allowed: true
      }))
    )
  },
  gerente_precatorio: {
    name: 'Gerente de Precatório',
    description: 'Gerenciamento de precatórios e equipe',
    color: '#3b82f6',
    icon: 'UserCheck',
    permissions: [
      { resource_type: 'precatorios', action: 'view', allowed: true },
      { resource_type: 'precatorios', action: 'create', allowed: true },
      { resource_type: 'precatorios', action: 'edit', allowed: true },
      { resource_type: 'precatorios', action: 'approve', allowed: true },
      { resource_type: 'clientes', action: 'view', allowed: true },
      { resource_type: 'clientes', action: 'create', allowed: true },
      { resource_type: 'clientes', action: 'edit', allowed: true },
      { resource_type: 'tasks', action: 'view', allowed: true },
      { resource_type: 'tasks', action: 'create', allowed: true },
      { resource_type: 'tasks', action: 'edit', allowed: true },
      { resource_type: 'tasks', action: 'assign', allowed: true },
      { resource_type: 'dashboard', action: 'view', allowed: true },
      { resource_type: 'dashboard', action: 'view_team_performance', allowed: true },
      { resource_type: 'reports', action: 'view', allowed: true },
      { resource_type: 'reports', action: 'create', allowed: true }
    ]
  },
  gerente_operacional: {
    name: 'Gerente Operacional',
    description: 'Gerenciamento operacional e equipe',
    color: '#10b981',
    icon: 'Users',
    permissions: [
      { resource_type: 'clientes', action: 'view', allowed: true },
      { resource_type: 'clientes', action: 'create', allowed: true },
      { resource_type: 'clientes', action: 'edit', allowed: true },
      { resource_type: 'tasks', action: 'view', allowed: true },
      { resource_type: 'tasks', action: 'create', allowed: true },
      { resource_type: 'tasks', action: 'edit', allowed: true },
      { resource_type: 'tasks', action: 'assign', allowed: true },
      { resource_type: 'dashboard', action: 'view', allowed: true },
      { resource_type: 'dashboard', action: 'view_team_performance', allowed: true },
      { resource_type: 'reports', action: 'view', allowed: true }
    ]
  },
  assistente: {
    name: 'Assistente',
    description: 'Acesso básico para execução de tarefas',
    color: '#8b5cf6',
    icon: 'User',
    permissions: [
      { resource_type: 'clientes', action: 'view', allowed: true },
      { resource_type: 'tasks', action: 'view', allowed: true },
      { resource_type: 'tasks', action: 'edit', allowed: true },
      { resource_type: 'dashboard', action: 'view', allowed: true }
    ]
  }
} as const;

// Cache para permissões
const enhancedPermissionsCache = new Map<string, { data: EnhancedUserPermissionsData; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutos

/**
 * Obtém todas as permissões de um usuário de forma otimizada
 */
export async function getEnhancedUserPermissions(userId: string): Promise<EnhancedUserPermissionsData> {
  try {
    // Verificar cache
    const cached = enhancedPermissionsCache.get(userId);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      return cached.data;
    }

    // Buscar dados do usuário
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('role, custom_role_id')
      .eq('id', userId)
      .single();

    if (profileError) {
      throw new Error(`Erro ao buscar perfil: ${profileError.message}`);
    }

    // Buscar permissões padrão do role
    const { data: rolePermissions, error: roleError } = await supabase
      .from('role_default_permissions')
      .select('resource_type, action, allowed')
      .eq('role_id', profile.custom_role_id || profile.role);

    // Buscar permissões específicas do usuário
    const { data: specificPermissions, error: specificError } = await supabase
      .from('user_specific_permissions')
      .select('resource_type, action, allowed')
      .eq('user_id', userId);

    const permissions: EnhancedUserPermissionsData = {
      role_permissions: rolePermissions || [],
      specific_permissions: specificPermissions || [],
      task_visibility: {
        can_see_own_tasks: true,
        can_see_team_tasks: profile.role !== 'assistente',
        can_see_all_tasks: profile.role === 'admin',
        visible_user_ids: []
      },
      page_access: AVAILABLE_PAGES.map(page => ({
        page_path: page.path,
        can_access: getDefaultPageAccess(profile.role, page.path)
      })),
      data_visibility: {
        can_see_own_data: true,
        can_see_team_data: profile.role !== 'assistente',
        can_see_all_data: profile.role === 'admin',
        visible_user_ids: [],
        restricted_fields: profile.role === 'assistente' ? ['financial_data'] : []
      }
    };

    // Salvar no cache
    enhancedPermissionsCache.set(userId, {
      data: permissions,
      timestamp: Date.now()
    });

    return permissions;
  } catch (error) {
    console.error('Erro ao obter permissões:', error);
    throw error;
  }
}

/**
 * Verifica se um usuário tem uma permissão específica
 */
export async function hasEnhancedPermission(
  userId: string,
  resourceType: string,
  action: string,
  resourceId?: string
): Promise<boolean> {
  try {
    const permissions = await getEnhancedUserPermissions(userId);

    // Verificar permissões específicas primeiro
    const specificPermission = permissions.specific_permissions.find(
      p => p.resource_type === resourceType && 
           p.action === action &&
           (!resourceId || p.resource_id === resourceId)
    );

    if (specificPermission !== undefined) {
      return specificPermission.allowed;
    }

    // Verificar permissões do role
    const rolePermission = permissions.role_permissions.find(
      p => p.resource_type === resourceType && p.action === action
    );

    return rolePermission?.allowed || false;
  } catch (error) {
    console.error('Erro ao verificar permissão:', error);
    return false;
  }
}

/**
 * Obtém acesso padrão a páginas baseado no role
 */
function getDefaultPageAccess(role: string, pagePath: string): boolean {
  const adminOnlyPages = ['/users', '/permissions', '/settings'];
  const managerPages = ['/dashboard', '/precatorios', '/clientes', '/tasks', '/calendar', '/documents', '/reports'];
  const basicPages = ['/dashboard', '/tasks', '/calendar'];

  if (role === 'admin') {
    return true;
  }

  if (adminOnlyPages.includes(pagePath)) {
    return false;
  }

  if (role === 'gerente_precatorio' || role === 'gerente_operacional') {
    return managerPages.includes(pagePath);
  }

  if (role === 'assistente') {
    return basicPages.includes(pagePath);
  }

  return false;
}

/**
 * Limpa o cache de permissões
 */
export function clearEnhancedPermissionsCache(userId?: string): void {
  if (userId) {
    enhancedPermissionsCache.delete(userId);
  } else {
    enhancedPermissionsCache.clear();
  }
}

import {
  Mail,
  Phone,
  FileText,
  Users,
  Target,
  TrendingUp,
  Clock,
  Award,
  Star,
  Building2,
  Settings,
  UserCircle,
  Bell,
  ChevronRight,
  RefreshCw,
  Edit,
  Briefcase,
  Calendar,
  ListChecks
} from 'lucide-react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AreaChart, Area, XAxis, YAxis, Tooltip, Legend, ResponsiveContainer, BarChart, Bar, Pie, PieChart } from 'recharts';
import { AssociatedTasks } from '@/components/tasks/AssociatedTasks';
import { PermissionStatus } from '@/components/permissions/PermissionStatus';
import { CHART_COLORS } from '@/constants/chartColors';
import { useState, forwardRef, ElementRef, ComponentPropsWithoutRef } from 'react';
import { toast } from 'sonner';
import { TopNav } from '@/components/top-nav';
import { useParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useUserProfile } from '@/hooks/useUserProfile';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Tipos para os dados do usuário
type UserRole = "admin" | "gerente_precatorio" | "gerente_operacional" | "assistente";

interface UserProfile {
  id: string;
  email: string;
  nome?: string;
  role: UserRole;
  foto_url?: string;
  status?: string;
  created_at?: string;
  telefone?: string;
  cargo?: string;
  departamento?: string;
  data_entrada?: string;
  metasMensais?: {
    precatorios?: { atual: number, meta: number, progresso: number },
    clientes?: { atual: number, meta: number, progresso: number },
    faturamento?: { atual: string, meta: string, progresso: number }
  };
  habilidades?: string[];
  certificacoes?: Array<{ nome: string, instituicao: string, ano: string }>;
}

// Dados de exemplo
const desempenhoMensal = [
  { mes: 'Jan', precatorios: 12, clientes: 5, meta: 15, tarefas: 28 },
  { mes: 'Fev', precatorios: 15, clientes: 7, meta: 15, tarefas: 32 },
  { mes: 'Mar', precatorios: 18, clientes: 8, meta: 15, tarefas: 35 },
  { mes: 'Abr', precatorios: 14, clientes: 6, meta: 15, tarefas: 30 },
  { mes: 'Mai', precatorios: 16, clientes: 9, meta: 15, tarefas: 33 },
  { mes: 'Jun', precatorios: 20, clientes: 11, meta: 15, tarefas: 38 },
];

const precatoriosAtivos = [
  {
    numero: '2024.001.123',
    cliente: 'Empresa ABC Ltda',
    tipo: 'Tributário',
    status: 'Em andamento',
    prazo: '15/04/2024',
    valor: 'R$ 50.000,00',
    progresso: 65,
    responsavel: 'Ana Silva',
    prioridade: 'Média'
  },
  {
    numero: '2024.001.456',
    cliente: 'Indústrias XYZ',
    tipo: 'Trabalhista',
    status: 'Urgente',
    prazo: '10/04/2024',
    valor: 'R$ 35.000,00',
    progresso: 40,
    responsavel: 'Ana Silva',
    prioridade: 'Alta'
  },
  {
    numero: '2024.001.789',
    cliente: 'Comércio Fast',
    tipo: 'Cível',
    status: 'Em análise',
    prazo: '20/04/2024',
    valor: 'R$ 25.000,00',
    progresso: 25,
    responsavel: 'Carlos Mendes',
    prioridade: 'Baixa'
  },
  {
    numero: '2024.001.234',
    cliente: 'Transportes Rápidos',
    tipo: 'Administrativo',
    status: 'Em andamento',
    prazo: '25/04/2024',
    valor: 'R$ 42.000,00',
    progresso: 50,
    responsavel: 'Ana Silva',
    prioridade: 'Média'
  }
];

// Adicionando mais dados para clientes
const clientesCaptados = [
  {
    nome: 'Empresa ABC Ltda',
    setor: 'Tecnologia',
    data: '01/03/2024',
    status: 'Ativo',
    valor: 'R$ 8.500,00',
    contato: 'João Silva',
    email: '<EMAIL>',
    telefone: '(11) 98765-4321',
    precatorios: 3,
    tarefas: 5
  },
  {
    nome: 'Indústrias XYZ',
    setor: 'Indústria',
    data: '15/03/2024',
    status: 'Em negociação',
    valor: 'R$ 12.000,00',
    contato: 'Maria Oliveira',
    email: '<EMAIL>',
    telefone: '(11) 91234-5678',
    precatorios: 2,
    tarefas: 3
  },
  {
    nome: 'Comércio Fast',
    setor: 'Varejo',
    data: '28/03/2024',
    status: 'Ativo',
    valor: 'R$ 5.500,00',
    contato: 'Pedro Santos',
    email: '<EMAIL>',
    telefone: '(11) 99876-5432',
    precatorios: 1,
    tarefas: 2
  },
  {
    nome: 'Transportes Rápidos',
    setor: 'Logística',
    data: '10/04/2024',
    status: 'Ativo',
    valor: 'R$ 7.800,00',
    contato: 'Ana Costa',
    email: '<EMAIL>',
    telefone: '(11) 98765-1234',
    precatorios: 1,
    tarefas: 4
  }
];

const indicadoresDesempenho = [
  {
    titulo: 'Precatórios Ativos',
    valor: 45,
    meta: 50,
    variacao: '+8',
    icon: <FileText className="w-4 h-4" />,
    progresso: 90
  },
  {
    titulo: 'Clientes Ativos',
    valor: 28,
    meta: 30,
    variacao: '+3',
    icon: <Users className="w-4 h-4" />,
    progresso: 93
  },
  {
    titulo: 'Taxa de Sucesso',
    valor: '92%',
    meta: '95%',
    variacao: '+2%',
    icon: <Target className="w-4 h-4" />,
    progresso: 92
  },
  {
    titulo: 'Faturamento',
    valor: 'R$ 185K',
    meta: 'R$ 200K',
    variacao: '+15K',
    icon: <TrendingUp className="w-4 h-4" />,
    progresso: 92.5
  }
];

const conquistas = [
  {
    titulo: 'Top Performer',
    descricao: 'Melhor desempenho do mês',
    data: 'Março 2024',
    icon: <Star className="w-4 h-4" />
  },
  {
    titulo: 'Meta Atingida',
    descricao: '100% das metas do trimestre',
    data: 'Q1 2024',
    icon: <Target className="w-4 h-4" />
  },
  {
    titulo: 'Cliente Premium',
    descricao: 'Captação de cliente premium',
    data: 'Fevereiro 2024',
    icon: <Award className="w-4 h-4" />
  }
];

// Adicionando dados de tarefas
const tarefasAtivas = [
  {
    id: 'T001',
    titulo: 'Análise de documentação',
    precatorio: '2024.001.123',
    cliente: 'Empresa ABC Ltda',
    prazo: '12/04/2024',
    status: 'Em andamento',
    prioridade: 'Alta',
    progresso: 70
  },
  {
    id: 'T002',
    titulo: 'Elaboração de petição',
    precatorio: '2024.001.456',
    cliente: 'Indústrias XYZ',
    prazo: '08/04/2024',
    status: 'Pendente',
    prioridade: 'Urgente',
    progresso: 30
  },
  {
    id: 'T003',
    titulo: 'Reunião com cliente',
    precatorio: '2024.001.789',
    cliente: 'Comércio Fast',
    prazo: '15/04/2024',
    status: 'Agendada',
    prioridade: 'Média',
    progresso: 0
  },
  {
    id: 'T004',
    titulo: 'Revisão de cálculos',
    precatorio: '2024.001.234',
    cliente: 'Transportes Rápidos',
    prazo: '18/04/2024',
    status: 'Em andamento',
    prioridade: 'Alta',
    progresso: 45
  },
  {
    id: 'T005',
    titulo: 'Protocolo de documentos',
    precatorio: '2024.001.123',
    cliente: 'Empresa ABC Ltda',
    prazo: '20/04/2024',
    status: 'Pendente',
    prioridade: 'Baixa',
    progresso: 10
  }
];

// Estatísticas de precatórios por tipo
const estatisticasPrecatorios = [
  { tipo: 'Tributário', quantidade: 18, valor: 'R$ 850.000,00' },
  { tipo: 'Trabalhista', quantidade: 12, valor: 'R$ 420.000,00' },
  { tipo: 'Cível', quantidade: 9, valor: 'R$ 320.000,00' },
  { tipo: 'Administrativo', quantidade: 6, valor: 'R$ 210.000,00' }
];

// Dados para gráfico de distribuição de tarefas
const distribuicaoTarefas = [
  { status: 'Concluídas', quantidade: 45 },
  { status: 'Em andamento', quantidade: 28 },
  { status: 'Pendentes', quantidade: 15 },
  { status: 'Atrasadas', quantidade: 7 }
];

// Estatísticas de clientes por setor
const estatisticasClientes = [
  { setor: 'Tecnologia', quantidade: 12, valor: 'R$ 95.000,00' },
  { setor: 'Indústria', quantidade: 8, valor: 'R$ 120.000,00' },
  { setor: 'Varejo', quantidade: 15, valor: 'R$ 85.000,00' },
  { setor: 'Logística', quantidade: 5, valor: 'R$ 45.000,00' }
];

// Adicionando dados de notificações
const notificacoesRecentes = [
  {
    id: 1,
    titulo: 'Novo precatório atribuído',
    descricao: 'Você foi designado como responsável pelo precatório #2024.001.234',
    data: '2 horas atrás',
    lida: false,
    tipo: 'precatorio'
  },
  {
    id: 2,
    titulo: 'Prazo próximo',
    descricao: 'A tarefa "Elaboração de petição" vence em 2 dias',
    data: '5 horas atrás',
    lida: true,
    tipo: 'tarefa'
  },
  {
    id: 3,
    titulo: 'Meta atingida',
    descricao: 'Parabéns! Você atingiu sua meta de clientes para o mês',
    data: '1 dia atrás',
    lida: true,
    tipo: 'meta'
  },
  {
    id: 4,
    titulo: 'Novo cliente cadastrado',
    descricao: 'O cliente "Transportes Rápidos" foi cadastrado com sucesso',
    data: '2 dias atrás',
    lida: true,
    tipo: 'cliente'
  }
];

// Primeiro, vamos criar um componente Progress personalizado
const CustomProgress = forwardRef<
  ElementRef<typeof Progress>,
  ComponentPropsWithoutRef<typeof Progress> & { indicatorColor?: string }
>(({ className, indicatorColor, ...props }, ref) => (
  <Progress
    ref={ref}
    className={className}
    {...props}
  />
));

export default function UserProfile() {
  const [mostrarNotificacoes, setMostrarNotificacoes] = useState(false);
  const [isViewingOtherUser, setIsViewingOtherUser] = useState(false);

  const { id: userId } = useParams();
  const { user: currentUser } = useAuth();

  // Usar o hook personalizado para dados do perfil
  const {
    profileData,
    metrics,
    performanceData,
    loading,
    error,
    refreshData
  } = useUserProfile(userId);

  // Verificar se está visualizando outro usuário
  const targetUserId = userId || currentUser?.id;
  const isOtherUser = userId && userId !== currentUser?.id;

  // Verificar permissões para visualizar outro usuário
  if (isOtherUser && currentUser?.role !== 'admin') {
    return (
      <div className="flex flex-col h-screen w-screen overflow-hidden">
        <TopNav
          title="Acesso Negado"
          icon={<UserCircle className="h-6 w-6 text-primary" />}
        />
        <div className="flex-1 p-6 pt-20">
          <Alert variant="destructive">
            <UserCircle className="h-4 w-4" />
            <AlertTitle>Acesso Negado</AlertTitle>
            <AlertDescription>
              Você não tem permissão para visualizar este perfil. Apenas administradores podem visualizar perfis de outros usuários.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  // Mapear o papel para um texto amigável
  const getRoleText = (role: UserRole) => {
    const roles = {
      admin: 'Administrador',
      gerente_precatorio: 'Gerente de Precatório',
      gerente_operacional: 'Gerente Operacional',
      assistente: 'Assistente'
    };
    return roles[role] || 'Usuário';
  };

  // Indicadores de desempenho baseados nos dados reais
  const indicadoresDesempenho = [
    {
      titulo: "Precatórios Concluídos",
      valor: String(metrics?.precatoriosConcluidos || 0),
      meta: String(metrics?.totalPrecatorios || 0),
      progresso: metrics?.totalPrecatorios ? Math.round((metrics.precatoriosConcluidos / metrics.totalPrecatorios) * 100) : 0,
      variacao: "+12%",
      icon: <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400" />
    },
    {
      titulo: "Clientes Ativos",
      valor: String(metrics?.clientesAtivos || 0),
      meta: String(metrics?.totalClientes || 0),
      progresso: metrics?.totalClientes ? Math.round((metrics.clientesAtivos / metrics.totalClientes) * 100) : 0,
      variacao: "+25%",
      icon: <Users className="w-5 h-5 text-green-600 dark:text-green-400" />
    },
    {
      titulo: "Tarefas Finalizadas",
      valor: String(metrics?.tarefasConcluidas || 0),
      meta: String(metrics?.totalTarefas || 0),
      progresso: metrics?.totalTarefas ? Math.round((metrics.tarefasConcluidas / metrics.totalTarefas) * 100) : 0,
      variacao: "+8%",
      icon: <Target className="w-5 h-5 text-amber-600 dark:text-amber-400" />
    },
    {
      titulo: "Tarefas Pendentes",
      valor: String(metrics?.tarefasPendentes || 0),
      meta: String(metrics?.totalTarefas || 0),
      progresso: metrics?.totalTarefas ? Math.round(((metrics.totalTarefas - metrics.tarefasPendentes) / metrics.totalTarefas) * 100) : 0,
      variacao: "+2%",
      icon: <Clock className="w-5 h-5 text-purple-600 dark:text-purple-400" />
    }
  ];

  if (loading) {
    return (
      <div className="flex flex-col h-screen w-screen overflow-hidden">
        <TopNav
          title="Carregando Perfil..."
          icon={<UserCircle className="h-6 w-6 text-primary" />}
        />
        <div className="flex-1 flex items-center justify-center pt-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Carregando dados do usuário...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !profileData) {
    return (
      <div className="flex flex-col h-screen w-screen overflow-hidden">
        <TopNav
          title="Erro no Perfil"
          icon={<UserCircle className="h-6 w-6 text-primary" />}
        />
        <div className="flex-1 p-6 pt-20">
          <Alert variant="destructive">
            <UserCircle className="h-4 w-4" />
            <AlertTitle>Perfil não encontrado</AlertTitle>
            <AlertDescription>
              {error || 'Não foi possível carregar os dados do perfil. Por favor, tente novamente.'}
            </AlertDescription>
          </Alert>
          <Button
            onClick={() => refreshData()}
            className="mt-4"
            variant="outline"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Tentar Novamente
          </Button>
        </div>
      </div>
    );
  }

  // Função para alternar a exibição de notificações
  const toggleNotificacoes = () => {
    setMostrarNotificacoes(prev => !prev);
  };

  return (
    <div className="flex flex-col h-screen w-screen overflow-hidden">
      <TopNav
        title={isOtherUser ? `Perfil de ${profileData?.nome || profileData?.email}` : "Meu Perfil"}
        icon={<UserCircle className="h-6 w-6 text-primary" />}
      />

      <div className="flex-1 p-2 md:p-10 pt-20 overflow-auto rounded-tl-2xl border border-neutral-200 dark:border-neutral-700 bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-black flex flex-col gap-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>

            <p className="text-neutral-600 dark:text-neutral-400">
              Visão geral e desempenho
            </p>
          </div>
          <div className="flex gap-2">
            <div className="relative">
              <Button
                variant="outline"
                className="gap-2 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:bg-neutral-100 dark:hover:bg-neutral-700"
                onClick={toggleNotificacoes}
              >
                <Bell className="w-4 h-4" />
                Notificações
                <Badge className="ml-1 h-5 w-5 p-0 flex items-center justify-center">
                  {performanceData?.notificacoes?.filter(n => !n.lida).length || 0}
                </Badge>
              </Button>

              {mostrarNotificacoes && (
                <Card className="absolute right-0 top-full mt-2 w-80 z-50 border-neutral-200 dark:border-neutral-700 bg-white/95 dark:bg-neutral-800/95 backdrop-blur-sm">
                  <CardHeader className="py-3">
                    <CardTitle className="text-sm font-medium">Notificações Recentes</CardTitle>
                  </CardHeader>
                  <CardContent className="py-0 px-2">
                    <div className="max-h-80 overflow-y-auto">
                      {(performanceData?.notificacoes || []).map((notificacao) => (
                        <div
                          key={notificacao.id}
                          className={`p-3 mb-2 rounded-lg ${notificacao.lida ? 'bg-transparent' : 'bg-blue-50 dark:bg-blue-900/20'} hover:bg-neutral-100 dark:hover:bg-neutral-700/50 cursor-pointer transition-colors`}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`p-2 rounded-full ${
                              notificacao.tipo === 'precatorio' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' :
                              notificacao.tipo === 'tarefa' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400' :
                              notificacao.tipo === 'meta' ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400' :
                              'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400'
                            }`}>
                              {notificacao.tipo === 'precatorio' ? <FileText className="w-4 h-4" /> :
                               notificacao.tipo === 'tarefa' ? <Clock className="w-4 h-4" /> :
                               notificacao.tipo === 'meta' ? <Target className="w-4 h-4" /> :
                               <Users className="w-4 h-4" />}
                            </div>
                            <div className="flex-1">
                              <p className="text-sm font-medium">{notificacao.titulo}</p>
                              <p className="text-xs text-neutral-500 mt-1">{notificacao.descricao}</p>
                              <p className="text-xs text-neutral-400 mt-2">{notificacao.data}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                  <div className="p-2 border-t border-neutral-200 dark:border-neutral-700">
                    <Button variant="ghost" size="sm" className="w-full justify-between">
                      Ver todas as notificações
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </Card>
              )}
            </div>
            <Button variant="outline" className="gap-2 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:bg-neutral-100 dark:hover:bg-neutral-700">
              <Settings className="w-4 h-4" />
              Configurações
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="border-neutral-200 dark:border-neutral-700 hover:shadow-lg transition-shadow bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Avatar className="h-24 w-24 mb-4 border-2 border-white shadow-md">
                    <AvatarImage src={profileData.foto_url || ""} />
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white text-lg">
                      {profileData.nome
                        ? profileData.nome.split(' ').map((n: string) => n[0]).join('').substring(0, 2).toUpperCase()
                        : profileData.email?.substring(0, 2).toUpperCase() || "??"}
                    </AvatarFallback>
                  </Avatar>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200 mb-1">
                    {profileData.nome || profileData.email?.split('@')[0]}
                  </h2>
                  <p className="text-sm text-neutral-500 mb-2">{getRoleText(profileData.role as UserRole)}</p>
                  <div className="flex justify-center gap-2 mb-4">
                    <Badge variant="secondary" className="bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/40">
                      {profileData.status === 'ativo' ? 'Ativo' : 'Inativo'}
                    </Badge>
                  </div>
                </motion.div>

                <div className="w-full space-y-3 mt-2">
                  <div className="flex items-center gap-3 text-sm text-neutral-600 dark:text-neutral-400">
                    <div className="p-1.5 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                      <Mail className="w-3.5 h-3.5" />
                    </div>
                    <span>{profileData.email}</span>
                  </div>
                  {profileData.telefone && (
                    <div className="flex items-center gap-3 text-sm text-neutral-600 dark:text-neutral-400">
                      <div className="p-1.5 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                        <Phone className="w-3.5 h-3.5" />
                      </div>
                      <span>{profileData.telefone}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-3 text-sm text-neutral-600 dark:text-neutral-400">
                    <div className="p-1.5 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                      <Building2 className="w-3.5 h-3.5" />
                    </div>
                    <span>
                      {profileData.data_entrada
                        ? new Date(profileData.data_entrada).toLocaleDateString('pt-BR')
                        : 'Data não disponível'}
                    </span>
                  </div>
                </div>

                <div className="w-full mt-6 pt-6 border-t border-neutral-200 dark:border-neutral-700">
                  <h3 className="text-sm font-medium mb-3 text-left">Metas Mensais</h3>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs text-neutral-500">Precatórios</span>
                        <span className="text-xs font-medium">{metrics?.metasMensais?.precatorios?.atual || 0}/{metrics?.metasMensais?.precatorios?.meta || 0}</span>
                      </div>
                      <CustomProgress
                        value={metrics?.metasMensais?.precatorios?.progresso || 0}
                        className="h-2 [&>div]:bg-blue-500"
                      />
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs text-neutral-500">Clientes</span>
                        <span className="text-xs font-medium">{metrics?.metasMensais?.clientes?.atual || 0}/{metrics?.metasMensais?.clientes?.meta || 0}</span>
                      </div>
                      <CustomProgress
                        value={metrics?.metasMensais?.clientes?.progresso || 0}
                        className="h-2 [&>div]:bg-green-500"
                      />
                    </div>
                    <div>
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-xs text-neutral-500">Faturamento</span>
                        <span className="text-xs font-medium">{metrics?.metasMensais?.faturamento?.atual || 'R$ 0'}</span>
                      </div>
                      <CustomProgress
                        value={metrics?.metasMensais?.faturamento?.progresso || 0}
                        className="h-2 [&>div]:bg-amber-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Informações adicionais do perfil */}
                <div className="w-full mt-6 pt-6 border-t border-neutral-200 dark:border-neutral-700">
                  <h3 className="text-sm font-medium mb-3 text-left">Informações Adicionais</h3>
                  <div className="space-y-2 text-sm text-neutral-600 dark:text-neutral-400">
                    {profileData.cargo && (
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Cargo:</span>
                        <span>{profileData.cargo}</span>
                      </div>
                    )}
                    {profileData.departamento && (
                      <div className="flex items-center gap-2">
                        <span className="font-medium">Departamento:</span>
                        <span>{profileData.departamento}</span>
                      </div>
                    )}
                    <div className="flex items-center gap-2">
                      <span className="font-medium">Membro desde:</span>
                      <span>
                        {profileData.created_at
                          ? new Date(profileData.created_at).toLocaleDateString('pt-BR')
                          : 'Data não disponível'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="w-full mt-6 pt-6 border-t border-neutral-200 dark:border-neutral-700">
                  <h3 className="text-sm font-medium mb-3 text-left">Permissões</h3>
                  <PermissionStatus />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="lg:col-span-2">
            <Tabs defaultValue="overview" className="w-full">
              <TabsList className="bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                <TabsTrigger value="overview">Visão Geral</TabsTrigger>
                <TabsTrigger value="processes">Precatórios</TabsTrigger>
                <TabsTrigger value="clients">Clientes</TabsTrigger>
                <TabsTrigger value="tasks">Tarefas</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
              </TabsList>

              <TabsContent value="overview" className="mt-4">
                <div className="grid gap-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {indicadoresDesempenho.map((indicador, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card key={index} className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:shadow-md transition-shadow">
                          <CardContent className="p-6">
                            <div className="flex items-center justify-between mb-4">
                              <div className={`p-2 rounded-lg ${
                                indicador.progresso >= 90 ? 'bg-green-100 dark:bg-green-900/20' :
                                indicador.progresso >= 80 ? 'bg-blue-100 dark:bg-blue-900/20' :
                                'bg-amber-100 dark:bg-amber-900/20'
                              }`}>
                                {indicador.icon}
                              </div>
                              <Badge variant={indicador.progresso >= 90 ? "default" : "secondary"} className="text-xs">
                                {indicador.variacao}
                              </Badge>
                            </div>
                            <h3 className="text-sm text-neutral-500 mb-1">{indicador.titulo}</h3>
                            <div className="flex items-baseline gap-2">
                              <span className="text-2xl font-semibold text-neutral-800 dark:text-neutral-200">{indicador.valor}</span>
                              <span className="text-sm text-neutral-500">/ {indicador.meta}</span>
                            </div>
                            <CustomProgress
                              value={indicador.progresso}
                              className={`h-2 mt-4 ${
                                indicador.progresso >= 90 ? '[&>div]:bg-green-500' :
                                indicador.progresso >= 80 ? '[&>div]:bg-blue-500' :
                                '[&>div]:bg-amber-500'
                              }`}
                            />
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-lg">Desempenho Mensal</CardTitle>
                        <div className="flex items-center gap-2">
                          <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                          <span className="text-xs text-neutral-500">Precatórios</span>
                          <div className="w-3 h-3 rounded-full bg-neutral-500"></div>
                          <span className="text-xs text-neutral-500">Clientes</span>
                          <div className="w-3 h-3 rounded-full bg-green-500"></div>
                          <span className="text-xs text-neutral-500">Tarefas</span>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[300px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <AreaChart data={performanceData?.desempenhoMensal || []}>
                              <defs>
                                <linearGradient id="colorPrecatorios" x1="0" y1="0" x2="0" y2="1">
                                  <stop offset="5%" stopColor={CHART_COLORS.primary.main} stopOpacity={0.1}/>
                                  <stop offset="95%" stopColor={CHART_COLORS.primary.main} stopOpacity={0}/>
                                </linearGradient>
                                <linearGradient id="colorClientes" x1="0" y1="0" x2="0" y2="1">
                                  <stop offset="5%" stopColor={CHART_COLORS.primary.light} stopOpacity={0.1}/>
                                  <stop offset="95%" stopColor={CHART_COLORS.primary.light} stopOpacity={0}/>
                                </linearGradient>
                                <linearGradient id="colorTarefas" x1="0" y1="0" x2="0" y2="1">
                                  <stop offset="5%" stopColor={CHART_COLORS.secondary.main} stopOpacity={0.1}/>
                                  <stop offset="95%" stopColor={CHART_COLORS.secondary.main} stopOpacity={0}/>
                                </linearGradient>
                              </defs>
                              <XAxis dataKey="mes" stroke="#6b7280" />
                              <YAxis stroke="#6b7280" />
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.8)',
                                  borderRadius: '8px',
                                  border: '1px solid #e5e7eb',
                                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                                }}
                              />
                              <Legend />
                              <Area
                                type="monotone"
                                dataKey="precatorios"
                                stroke={CHART_COLORS.primary.main}
                                fillOpacity={1}
                                fill="url(#colorPrecatorios)"
                              />
                              <Area
                                type="monotone"
                                dataKey="clientes"
                                stroke={CHART_COLORS.primary.light}
                                fillOpacity={1}
                                fill="url(#colorClientes)"
                              />
                              <Area
                                type="monotone"
                                dataKey="tarefas"
                                stroke={CHART_COLORS.secondary.main}
                                fillOpacity={1}
                                fill="url(#colorTarefas)"
                              />
                            </AreaChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    <div className="grid grid-cols-1 gap-6">
                      <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:shadow-md transition-shadow">
                        <CardHeader>
                          <CardTitle className="text-lg">Conquistas Recentes</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-6">
                            {conquistas.map((conquista, index) => (
                              <motion.div
                                key={index}
                                initial={{ opacity: 0, x: -10 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="flex items-start gap-4"
                              >
                                <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                                  {conquista.icon}
                                </div>
                                <div>
                                  <h4 className="font-medium text-neutral-800 dark:text-neutral-200">{conquista.titulo}</h4>
                                  <p className="text-sm text-neutral-500">{conquista.descricao}</p>
                                  <p className="text-xs text-neutral-500 mt-1">{conquista.data}</p>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm hover:shadow-md transition-shadow">
                        <CardHeader>
                          <CardTitle className="text-lg">Próximos Prazos</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {tarefasAtivas
                              .sort((a, b) => {
                                const dateA = new Date(a.prazo.split('/').reverse().join('-'));
                                const dateB = new Date(b.prazo.split('/').reverse().join('-'));
                                return dateA.getTime() - dateB.getTime();
                              })
                              .slice(0, 3)
                              .map((tarefa, index) => (
                                <motion.div
                                  key={index}
                                  initial={{ opacity: 0, y: 5 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: index * 0.1 }}
                                  className="flex items-center justify-between p-3 rounded-lg border border-neutral-200 dark:border-neutral-700"
                                >
                                  <div className="flex items-center gap-2">
                                    <div className={`p-1.5 rounded-lg ${
                                      new Date(tarefa.prazo.split('/').reverse().join('-')) < new Date()
                                        ? 'bg-red-100 dark:bg-red-900/20'
                                        : 'bg-blue-100 dark:bg-blue-900/20'
                                    }`}>
                                      <Clock className={`w-3.5 h-3.5 ${
                                        new Date(tarefa.prazo.split('/').reverse().join('-')) < new Date()
                                          ? 'text-red-600 dark:text-red-400'
                                          : 'text-blue-600 dark:text-blue-400'
                                      }`} />
                                    </div>
                                    <div>
                                      <p className="text-sm font-medium">{tarefa.titulo}</p>
                                      <p className="text-xs text-neutral-500">{tarefa.prazo}</p>
                                    </div>
                                  </div>
                                  <Badge
                                    variant={
                                      tarefa.prioridade === 'Urgente' ? 'destructive' :
                                      tarefa.prioridade === 'Alta' ? 'default' :
                                      'secondary'
                                    }
                                  >
                                    {tarefa.prioridade}
                                  </Badge>
                                </motion.div>
                              ))}
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-lg">Precatórios por Tipo</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[250px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                  borderRadius: '8px',
                                  border: '1px solid #e5e7eb',
                                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                  padding: '10px'
                                }}
                              />
                              <Legend layout="vertical" verticalAlign="middle" align="right" />
                              <Pie
                                data={estatisticasPrecatorios.map((item, index) => ({
                                  name: item.tipo,
                                  value: item.quantidade,
                                  fill: [
                                    CHART_COLORS.primary.main,
                                    CHART_COLORS.primary.light,
                                    CHART_COLORS.primary.dark,
                                    CHART_COLORS.secondary.main
                                  ][index % 4]
                                }))}
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                innerRadius={40}
                                dataKey="value"
                                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                labelLine={false}
                              />
                            </PieChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-lg">Distribuição de Tarefas</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[250px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <BarChart data={distribuicaoTarefas} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                              <XAxis dataKey="status" />
                              <YAxis />
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                  borderRadius: '8px',
                                  border: '1px solid #e5e7eb',
                                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                  padding: '10px'
                                }}
                              />
                              <Legend />
                              <Bar dataKey="quantidade" fill={CHART_COLORS.primary.main} radius={[4, 4, 0, 0]} />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-lg">Precatórios Recentes</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {precatoriosAtivos
                            .slice(0, 3)
                            .map((precatorio, index) => (
                              <motion.div
                                key={index}
                                initial={{ opacity: 0, y: 5 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: index * 0.1 }}
                                className="flex items-center justify-between p-3 rounded-lg border border-neutral-200 dark:border-neutral-700"
                              >
                                <div className="flex items-center gap-2">
                                  <div className={`p-1.5 rounded-lg ${
                                    precatorio.prioridade === 'Alta' ? 'bg-red-100 dark:bg-red-900/20' :
                                    precatorio.prioridade === 'Média' ? 'bg-amber-100 dark:bg-amber-900/20' :
                                    'bg-green-100 dark:bg-green-900/20'
                                  }`}>
                                    <FileText className={`w-3.5 h-3.5 ${
                                      precatorio.prioridade === 'Alta' ? 'text-red-600 dark:text-red-400' :
                                      precatorio.prioridade === 'Média' ? 'text-amber-600 dark:text-amber-400' :
                                      'text-green-600 dark:text-green-400'
                                    }`} />
                                  </div>
                                  <div>
                                    <p className="text-sm font-medium">{precatorio.numero}</p>
                                    <p className="text-xs text-neutral-500">{precatorio.cliente}</p>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <p className="text-sm font-medium">{precatorio.valor}</p>
                                  <p className="text-xs text-neutral-500">{precatorio.prazo}</p>
                                </div>
                              </motion.div>
                            ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="processes" className="mt-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-lg">Precatórios Ativos</CardTitle>
                        <Badge variant="outline" className="ml-2">
                          {precatoriosAtivos.length} total
                        </Badge>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {precatoriosAtivos.map((precatorio, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex flex-col p-4 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:shadow-md transition-shadow"
                            >
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-2">
                                  <div className={`p-2 rounded-lg ${
                                    precatorio.prioridade === 'Alta' ? 'bg-red-100 dark:bg-red-900/20' :
                                    precatorio.prioridade === 'Média' ? 'bg-amber-100 dark:bg-amber-900/20' :
                                    'bg-green-100 dark:bg-green-900/20'
                                  }`}>
                                    <FileText className={`w-4 h-4 ${
                                      precatorio.prioridade === 'Alta' ? 'text-red-600 dark:text-red-400' :
                                      precatorio.prioridade === 'Média' ? 'text-amber-600 dark:text-amber-400' :
                                      'text-green-600 dark:text-green-400'
                                    }`} />
                                  </div>
                                  <div>
                                    <p className="font-medium">{precatorio.numero}</p>
                                    <p className="text-xs text-neutral-500">Responsável: {precatorio.responsavel}</p>
                                  </div>
                                </div>
                                <Badge
                                  variant={
                                    precatorio.status === 'Urgente' ? 'destructive' :
                                    precatorio.status === 'Em andamento' ? 'default' :
                                    'outline'
                                  }
                                  className="ml-auto"
                                >
                                  {precatorio.status}
                                </Badge>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                <div>
                                  <p className="text-sm font-medium text-neutral-800 dark:text-neutral-200">{precatorio.cliente}</p>
                                  <div className="flex items-center gap-2 text-xs text-neutral-500 mt-1">
                                    <Briefcase className="w-3 h-3" />
                                    <span>{precatorio.tipo}</span>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <p className="font-medium text-neutral-800 dark:text-neutral-200">{precatorio.valor}</p>
                                  <div className="flex items-center justify-end gap-2 text-xs text-neutral-500 mt-1">
                                    <Clock className="w-3 h-3" />
                                    <span>Prazo: {precatorio.prazo}</span>
                                  </div>
                                </div>
                              </div>

                              <div className="mt-2">
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-xs text-neutral-500">Progresso</span>
                                  <span className="text-xs font-medium">{precatorio.progresso}%</span>
                                </div>
                                <CustomProgress
                                  value={precatorio.progresso}
                                  className={`h-2 ${
                                    precatorio.progresso < 30 ? 'bg-neutral-100 dark:bg-neutral-800' : ''
                                  } ${
                                    precatorio.progresso > 70 ? '[&>div]:bg-green-500' :
                                    precatorio.progresso > 40 ? '[&>div]:bg-amber-500' :
                                    '[&>div]:bg-red-500'
                                  }`}
                                />
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div>
                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm mb-6">
                      <CardHeader>
                        <CardTitle className="text-lg">Estatísticas</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {estatisticasPrecatorios.map((estatistica, index) => (
                            <div key={index} className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className={`p-2 rounded-lg bg-neutral-100 dark:bg-neutral-700`}>
                                  <FileText className="w-4 h-4" />
                                </div>
                                <div>
                                  <p className="font-medium text-sm">{estatistica.tipo}</p>
                                  <p className="text-xs text-neutral-500">{estatistica.quantidade} precatórios</p>
                                </div>
                              </div>
                              <p className="font-medium text-sm">{estatistica.valor}</p>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-lg">Distribuição por Status</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[250px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                  borderRadius: '8px',
                                  border: '1px solid #e5e7eb',
                                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                  padding: '10px'
                                }}
                              />
                              <Legend layout="vertical" verticalAlign="middle" align="right" />
                              <Pie
                                data={[
                                  { name: 'Em andamento', value: 2, fill: CHART_COLORS.primary.main },
                                  { name: 'Urgente', value: 1, fill: CHART_COLORS.primary.light },
                                  { name: 'Em análise', value: 1, fill: CHART_COLORS.secondary.main }
                                ]}
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                innerRadius={40}
                                dataKey="value"
                                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                labelLine={false}
                              />
                            </PieChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="clients" className="mt-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardHeader className="flex flex-row items-center justify-between pb-2">
                        <CardTitle className="text-lg">Clientes Captados</CardTitle>
                        <Badge variant="outline" className="ml-2">
                          {clientesCaptados.length} total
                        </Badge>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {clientesCaptados.map((cliente, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="flex flex-col p-4 rounded-lg border border-neutral-200 dark:border-neutral-700 hover:shadow-md transition-shadow"
                            >
                              <div className="flex items-center justify-between mb-3">
                                <div className="flex items-center gap-2">
                                  <div className={`p-2 rounded-lg ${
                                    cliente.status === 'Ativo' ? 'bg-green-100 dark:bg-green-900/20' :
                                    'bg-amber-100 dark:bg-amber-900/20'
                                  }`}>
                                    <Building2 className={`w-4 h-4 ${
                                      cliente.status === 'Ativo' ? 'text-green-600 dark:text-green-400' :
                                      'text-amber-600 dark:text-amber-400'
                                    }`} />
                                  </div>
                                  <div>
                                    <p className="font-medium">{cliente.nome}</p>
                                    <p className="text-xs text-neutral-500">Contato: {cliente.contato}</p>
                                  </div>
                                </div>
                                <Badge
                                  variant={cliente.status === 'Ativo' ? 'default' : 'secondary'}
                                  className="ml-auto"
                                >
                                  {cliente.status}
                                </Badge>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                <div>
                                  <div className="flex items-center gap-2 text-xs text-neutral-500 mb-1">
                                    <Briefcase className="w-3 h-3" />
                                    <span>{cliente.setor}</span>
                                  </div>
                                  <div className="flex items-center gap-2 text-xs text-neutral-500 mb-1">
                                    <Mail className="w-3 h-3" />
                                    <span>{cliente.email}</span>
                                  </div>
                                  <div className="flex items-center gap-2 text-xs text-neutral-500">
                                    <Phone className="w-3 h-3" />
                                    <span>{cliente.telefone}</span>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <p className="font-medium text-neutral-800 dark:text-neutral-200">{cliente.valor}</p>
                                  <div className="flex items-center justify-end gap-2 text-xs text-neutral-500 mt-1">
                                    <Calendar className="w-3 h-3" />
                                    <span>Captado em: {cliente.data}</span>
                                  </div>
                                  <div className="flex items-center justify-end gap-4 mt-2">
                                    <div className="flex items-center gap-1">
                                      <FileText className="w-3 h-3 text-blue-500" />
                                      <span className="text-xs">{cliente.precatorios} precatórios</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      <ListChecks className="w-3 h-3 text-green-500" />
                                      <span className="text-xs">{cliente.tarefas} tarefas</span>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              <div className="flex justify-end mt-2">
                                <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
                                  <FileText className="w-3.5 h-3.5 mr-1" />
                                  Ver precatórios
                                </Button>
                                <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
                                  <ListChecks className="w-3.5 h-3.5 mr-1" />
                                  Ver tarefas
                                </Button>
                                <Button variant="outline" size="sm" className="h-8 px-2 text-xs">
                                  <Edit className="w-3.5 h-3.5 mr-1" />
                                  Editar
                                </Button>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <div>
                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm mb-6">
                      <CardHeader>
                        <CardTitle className="text-lg">Estatísticas por Setor</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-6">
                          {estatisticasClientes.map((estatistica, index) => (
                            <div key={index} className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <div className={`p-2 rounded-lg bg-neutral-100 dark:bg-neutral-700`}>
                                  <Building2 className="w-4 h-4" />
                                </div>
                                <div>
                                  <p className="font-medium text-sm">{estatistica.setor}</p>
                                  <p className="text-xs text-neutral-500">{estatistica.quantidade} clientes</p>
                                </div>
                              </div>
                              <p className="font-medium text-sm">{estatistica.valor}</p>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                      <CardHeader>
                        <CardTitle className="text-lg">Distribuição por Status</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="h-[250px]">
                          <ResponsiveContainer width="100%" height="100%">
                            <PieChart>
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.95)',
                                  borderRadius: '8px',
                                  border: '1px solid #e5e7eb',
                                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                                  padding: '10px'
                                }}
                              />
                              <Legend layout="vertical" verticalAlign="middle" align="right" />
                              <Pie
                                data={[
                                  { name: 'Ativo', value: 3, fill: '#22c55e' },
                                  { name: 'Em negociação', value: 1, fill: '#f59e0b' }
                                ]}
                                cx="50%"
                                cy="50%"
                                outerRadius={80}
                                innerRadius={40}
                                dataKey="value"
                                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                                labelLine={false}
                              />
                            </PieChart>
                          </ResponsiveContainer>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="tasks" className="mt-6">
                {profileData && (
                  <AssociatedTasks
                    entityId={profileData.id}
                    entityType="usuario"
                    showHeader={true}
                  />
                )}
              </TabsContent>

              <TabsContent value="performance" className="mt-6">
                <Card className="border-neutral-200 dark:border-neutral-700 bg-white/80 dark:bg-neutral-800/80 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-lg">Performance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {indicadoresDesempenho.map((indicador, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="p-2 rounded-lg bg-neutral-100 dark:bg-neutral-700">
                              {indicador.icon}
                            </div>
                            <div>
                              <p className="font-medium text-sm">{indicador.titulo}</p>
                              <p className="text-xs text-neutral-500">{indicador.valor}</p>
                            </div>
                          </div>
                          <Badge variant={indicador.progresso >= 90 ? "default" : "secondary"} className="text-xs">
                            {indicador.variacao}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Adicionando o rodapé */}
      <Footer />
    </div>
  );
}

// Adicionando um componente de rodapé
const Footer = () => {
  return (
    <div className="p-4 border-t border-neutral-200 dark:border-neutral-700 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm">
      <div className="container mx-auto flex flex-col md:flex-row justify-between items-center gap-4">
        <div className="text-sm text-neutral-500 dark:text-neutral-400">
          © 2024 Sistema de Gestão de Precatórios. Todos os direitos reservados.
        </div>
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
            Termos de Uso
          </Button>
          <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
            Política de Privacidade
          </Button>
          <Button variant="ghost" size="sm" className="h-8 px-2 text-xs">
            Suporte
          </Button>
        </div>
      </div>
    </div>
  );
};